import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useNavigationState } from '../hooks/useNavigationState';
import { supabase } from '../lib/supabase';

// Mock Supabase
vi.mock('../lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn()
        }))
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({}))
      }))
    }))
  }
}));

// Mock certificate context
vi.mock('../contexts/CertificateContext', () => ({
  useCertificate: () => ({
    activeCertificateId: 'test-cert-id'
  })
}));

describe('useNavigationState - Unidirectional Status Progression', () => {
  const mockSupabase = supabase as any;
  
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should prevent status regression from zusammenfassung to earlier pages', async () => {
    // Mock current status as 'zusammenfassung'
    const mockSelect = vi.fn().mockResolvedValue({
      data: { status: 'zusammenfassung', certificate_type: 'WG/V' },
      error: null
    });
    
    const mockUpdate = vi.fn().mockResolvedValue({ error: null });
    
    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          single: mockSelect
        })
      }),
      update: () => ({
        eq: mockUpdate
      })
    });

    const { result } = renderHook(() => useNavigationState('WG/V'));

    // Wait for initial load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Try to mark an earlier page as visited (should be prevented)
    await act(async () => {
      await result.current.markPageAsVisited('objektdaten');
    });

    // Verify that update was not called (status regression prevented)
    expect(mockUpdate).not.toHaveBeenCalled();
  });

  it('should allow status progression from objektdaten to gebaeudedetails1', async () => {
    // Mock current status as 'objektdaten'
    const mockSelect = vi.fn().mockResolvedValue({
      data: { status: 'objektdaten', certificate_type: 'WG/V' },
      error: null
    });

    const mockEq = vi.fn().mockResolvedValue({ error: null });

    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          single: mockSelect
        })
      }),
      update: () => ({
        eq: mockEq
      })
    });

    const { result } = renderHook(() => useNavigationState('WG/V'));

    // Wait for initial load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Try to mark next page as visited (should be allowed)
    await act(async () => {
      await result.current.markPageAsVisited('gebaeudedetails1');
    });

    // Verify that eq was called with the certificate ID (indicating update was attempted)
    expect(mockEq).toHaveBeenCalledWith('test-cert-id');
  });

  it('should prevent status regression from payment-related statuses', async () => {
    // Mock current status as 'paid'
    const mockSelect = vi.fn().mockResolvedValue({
      data: { status: 'paid', certificate_type: 'WG/V' },
      error: null
    });
    
    const mockUpdate = vi.fn().mockResolvedValue({ error: null });
    
    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          single: mockSelect
        })
      }),
      update: () => ({
        eq: mockUpdate
      })
    });

    const { result } = renderHook(() => useNavigationState('WG/V'));

    // Wait for initial load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Try to mark any page as visited (should be prevented)
    await act(async () => {
      await result.current.markPageAsVisited('zusammenfassung');
    });

    // Verify that update was not called (regression from payment status prevented)
    expect(mockUpdate).not.toHaveBeenCalled();
  });

  it('should allow updating to zusammenfassung from any earlier page', async () => {
    // Mock current status as 'verbrauch'
    const mockSelect = vi.fn().mockResolvedValue({
      data: { status: 'verbrauch', certificate_type: 'WG/V' },
      error: null
    });

    const mockEq = vi.fn().mockResolvedValue({ error: null });

    mockSupabase.from.mockReturnValue({
      select: () => ({
        eq: () => ({
          single: mockSelect
        })
      }),
      update: () => ({
        eq: mockEq
      })
    });

    const { result } = renderHook(() => useNavigationState('WG/V'));

    // Wait for initial load
    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    // Try to mark zusammenfassung as visited (should be allowed)
    await act(async () => {
      await result.current.markPageAsVisited('zusammenfassung');
    });

    // Verify that eq was called with the certificate ID (indicating update was attempted)
    expect(mockEq).toHaveBeenCalledWith('test-cert-id');
  });
});
